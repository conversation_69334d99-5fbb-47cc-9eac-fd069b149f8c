[+3277 ms] I/flutter ( 5153): 714609135
[  +11 ms] I/flutter ( 5153): === LOGIN REQUEST ===
[   +2 ms] I/flutter ( 5153): URL: https://api.sepesha.com/api/login
[        ] I/flutter ( 5153): Headers: {Content-Type: application/json}
[        ] I/flutter ( 5153): Body: {"phone":714609135,"user_type":"driver"}
[        ] I/flutter ( 5153): ==================
[+2193 ms] I/flutter ( 5153): === LOGIN RESPONSE ===
[   +1 ms] I/flutter ( 5153): Status Code: 200
[        ] I/flutter ( 5153): Response Headers: {x-powered-by: PHP/8.3.23, alt-svc: h3=":443"; ma=2592000, cache-control: no-cache, private, access-control-allow-origin: *, date: Tue, 22 Jul 2025 07:40:52  
GMT, content-length: 175, content-type: application/json, via: 0.0 Caddy}
[   +1 ms] I/flutter ( 5153): Response Body: {"status":true,"message":"OPT Created
successfully","data":{"phone_number":"714609135","user_type":"driver","otp":4238,"is_verified":0,"otp_expires_at":"2025-07-22 07:43:52"}}
[        ] I/flutter ( 5153): ==================
[+4269 ms] D/InputMethodManager( 5153): showSoftInput() view=io.flutter.embedding.android.FlutterView{5be7382 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
[  +29 ms] I/AssistStructure( 5153): Flattened final assist data: 400 bytes, containing 1 windows, 3 views
[   +8 ms] D/InputConnectionAdaptor( 5153): The input method toggled cursor monitoring on
[  +83 ms] D/InsetsController( 5153): show(ime(), fromIme=true)
[+1301 ms] D/InputMethodManager( 5153): showSoftInput() view=io.flutter.embedding.android.FlutterView{5be7382 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
[  +30 ms] D/InputMethodManager( 5153): showSoftInput() view=io.flutter.embedding.android.FlutterView{5be7382 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
[  +19 ms] D/InputConnectionAdaptor( 5153): The input method toggled cursor monitoring on
[   +1 ms] I/AssistStructure( 5153): Flattened final assist data: 400 bytes, containing 1 windows, 3 views
[  +21 ms] D/InputConnectionAdaptor( 5153): The input method toggled cursor monitoring off
[   +4 ms] D/InputConnectionAdaptor( 5153): The input method toggled cursor monitoring on
[  +14 ms] D/InsetsController( 5153): show(ime(), fromIme=true)
[   +2 ms] D/InsetsController( 5153): show(ime(), fromIme=true)
[   +6 ms] D/InsetsController( 5153): show(ime(), fromIme=true)
[ +674 ms] D/InputMethodManager( 5153): showSoftInput() view=io.flutter.embedding.android.FlutterView{5be7382 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
[   +8 ms] D/InputMethodManager( 5153): showSoftInput() view=io.flutter.embedding.android.FlutterView{5be7382 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
[  +30 ms] D/InputConnectionAdaptor( 5153): The input method toggled cursor monitoring on
[   +2 ms] I/AssistStructure( 5153): Flattened final assist data: 400 bytes, containing 1 windows, 3 views
[  +26 ms] D/InputConnectionAdaptor( 5153): The input method toggled cursor monitoring off
[   +4 ms] D/InputConnectionAdaptor( 5153): The input method toggled cursor monitoring on
[  +23 ms] D/InsetsController( 5153): show(ime(), fromIme=true)
[   +1 ms] D/InsetsController( 5153): show(ime(), fromIme=true)
[   +4 ms] D/InsetsController( 5153): show(ime(), fromIme=true)
[ +631 ms] D/InputMethodManager( 5153): showSoftInput() view=io.flutter.embedding.android.FlutterView{5be7382 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
[ +113 ms] D/InputMethodManager( 5153): showSoftInput() view=io.flutter.embedding.android.FlutterView{5be7382 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
[   +4 ms] D/InputConnectionAdaptor( 5153): The input method toggled cursor monitoring on
[  +38 ms] I/AssistStructure( 5153): Flattened final assist data: 400 bytes, containing 1 windows, 3 views
[  +20 ms] D/InputConnectionAdaptor( 5153): The input method toggled cursor monitoring off
[   +4 ms] D/InputConnectionAdaptor( 5153): The input method toggled cursor monitoring on
[  +12 ms] D/InsetsController( 5153): show(ime(), fromIme=true)
[  +11 ms] D/InsetsController( 5153): show(ime(), fromIme=true)
[        ] D/InsetsController( 5153): show(ime(), fromIme=true)
[ +323 ms] I/flutter ( 5153): === VERIFY OTP REQUEST ===
[   +1 ms] I/flutter ( 5153): URL: https://api.sepesha.com/api/verify-otp
[        ] I/flutter ( 5153): Headers: {Content-Type: application/json}
[        ] I/flutter ( 5153): Body: {"phone":714609135,"otp":4238,"user_type":"driver"}
[        ] I/flutter ( 5153): ==================
[+1940 ms] I/flutter ( 5153): === VERIFY OTP RESPONSE ===
[        ] I/flutter ( 5153): Status Code: 200
[        ] I/flutter ( 5153): Response Headers: {x-powered-by: PHP/8.3.23, alt-svc: h3=":443"; ma=2592000, cache-control: no-cache, private, access-control-allow-origin: *, date: Tue, 22 Jul 2025 07:41:02  
GMT, content-encoding: gzip, vary: Accept-Encoding, content-length: 724, via: 0.0 Caddy, content-type: application/json}
[        ] I/flutter ( 5153): Response Body: {"status":true,"message":"OTP verified
successfully","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2tpdW5nby5zZXBlc2hhLmNvbSIsInN1YiI6MiwiaWF0IjoxNzUzMTcwMDYyLCJleHAiOjE3NTMzNDI4NjIsInVzZXJfdHlwZSI6ImRyaXZlciIsImF1dGh
fa2V5IjoiNDA1MzRkZDAtZmI5OS00MjYxLTkzYmUtMDczNWEyNzJmNzlhIiwiZGF0YSI6eyJmaXJzdF9uYW1lIjoidlZ2IiwibWlkZGxlX25hbWUiOiJNaWNoYWVsIiwibGFzdF9uYW1lIjoiRG9lIiwicGhvbmVfbnVtYmVyIjoiNzE0NjA5MTM1IiwicGhvbmVjb2RlIjoiM
jU1IiwiZW1haWwiOiJqb2hudi5kb2VAZXhhbXBsZS5jb20iLCJwcml2YWN5X2NoZWNrZWQiOjEsInVpZCI6IjQwNTM0ZGQwLWZiOTktNDI2MS05M2JlLTA3MzVhMjcyZjc5YSJ9fQ.vX5e1giulYgUOueQ3WhaypuQKGMz6jAR3NaXrQcIyM0","refresh_token":"eyJ0eX
AiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTMxNzAwNjIsImV4cCI6MTc1NTc2MjA2Mn0.Q8MRYjPLxSTab04dn9f7dIlYZvqc5pcVTfvo63hTNnc","user_data":{"first_name":"vVv","middle_name":"Michael","last_name":"Doe","phoneco
de":"255","phone_number":"714609135","email":"<EMAIL>","user_type":"driver","uid":"40534dd0-fb99-4261-93be-0735a272f79a"}}
[   +1 ms] I/flutter ( 5153): ===================
[  +11 ms] I/flutter ( 5153): User data saved to preferences
[+1064 ms] D/MapsInitializer( 5153): preferredRenderer: null
[   +1 ms] D/zzcc    ( 5153): preferredRenderer: null
[        ] I/zzcc    ( 5153): Making Creator dynamically
[  +38 ms] W/mpany.sepeshap( 5153): ClassLoaderContext classpath size mismatch. expected=1, found=0
(DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/apex/com.android.extservices/javalib/android.ext.adservices.jar**********]#PCL[/system/framework/com.android
.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/apex/com.android.extservices/javali
b/android.ext.adservices.jar**********]} | DLC[];PCL[])
[   +9 ms] I/DynamiteModule( 5153): Considering local module com.google.android.gms.maps_core_dynamite:0 and remote module com.google.android.gms.maps_core_dynamite:252130101
[        ] I/DynamiteModule( 5153): Selected remote version of com.google.android.gms.maps_core_dynamite, version >= 252130101
[        ] V/DynamiteModule( 5153): Dynamite loader version >= 2, using loadModule2NoCrashUtils
[  +22 ms] W/System  ( 5153): ClassLoader referenced unknown path: 
[   +5 ms] D/nativeloader( 5153): Configuring clns-6 for other apk . target_sdk_version=36, uses_libraries=,
library_path=/data/app/~~4dvlENgeESzcZBvZMa7cfA==/com.google.android.gms-HZfmX2H3MQu93nfHphp9UA==/lib/arm64:/data/app/~~4dvlENgeESzcZBvZMa7cfA==/com.google.android.gms-HZfmX2H3MQu93nfHphp9UA==/base.apk!/lib
/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
[ +818 ms] I/mpany.sepeshap( 5153): hiddenapi: Accessing hidden method Ldalvik/system/VMStack;->getStackClass2()Ljava/lang/Class; (runtime_flags=0, domain=core-platform, api=unsupported) from Lm140/gmg;
(domain=app) using reflection: allowed
[  +52 ms] D/nativeloader( 5153): Configuring clns-7 for other apk . target_sdk_version=35, uses_libraries=ALL,
library_path=/data/user_de/0/com.google.android.gms/app_chimera/m/0000000b/dl-MapsCoreDynamite.integ_252130101100400.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
[  +27 ms] D/nativeloader( 5153): Load /data/user_de/0/com.google.android.gms/app_chimera/m/0000000b/dl-MapsCoreDynamite.integ_252130101100400.apk!/lib/arm64-v8a/libgmm-jni.so using isolated ns clns-7
(caller=/data/user_de/0/com.google.android.gms/app_chimera/m/0000000b/dl-MapsCoreDynamite.integ_252130101100400.apk): ok
[   +2 ms] I/native  ( 5153): I0000 00:00:1753170063.989451    5153 jni_init.cc:30] Initializing JNI...
[   +6 ms] I/Google Android Maps SDK( 5153): Google Play services client version: 18020000
[  +82 ms] D/bo      ( 5153): SDK type: 1, version: 252130101
[  +26 ms] D/ho      ( 5153): maps_core_dynamite module version in use (0 represents standalone library): 252130101
[        ] D/ho      ( 5153): Added event: 109
[        ] D/ho      ( 5153): Added event: 112
[        ] D/MapsInitializer( 5153): loadedRenderer: LATEST
[        ] D/zzcc    ( 5153): preferredRenderer: null
[   +4 ms] D/bo      ( 5153): SDK type: 1, version: 252130101
[   +1 ms] I/Google Android Maps SDK( 5153): Google Play services package version: 252635029
[        ] I/Google Android Maps SDK( 5153): Google Play services maps renderer version(maps_core): 252130101
[   +3 ms] D/bo      ( 5153): SDK type: 1, version: 252130101
[   +1 ms] D/de      ( 5153): about to start loading native library asynchronously
[  +10 ms] W/x       ( 5153): Suppressed StrictMode policy violation: StrictModeDiskReadViolation
[   +6 ms] I/o       ( 5153): Using GMM server: https://clients4.google.com/glm/mmap
[        ] W/x       ( 5153): Suppressed StrictMode policy violation: StrictModeDiskReadViolation
[   +1 ms] W/x       ( 5153): Suppressed StrictMode policy violation: StrictModeDiskWriteViolation
[   +8 ms] D/o       ( 5153): Using Non-null serverVersionMetadataManager to load previous metadata.
[   +2 ms] D/o       ( 5153): Previous session server version metadata loaded: CggIBhCx6PjDBgoICAMQ1sviwwYKCggEELjg98MGGAEKCAgBEJ6cusMG
[  +10 ms] W/x       ( 5153): Suppressed StrictMode policy violation: StrictModeDiskReadViolation
[   +2 ms] D/DataRequestDispatcher( 5153): Included server version metadata in dimens
[ +144 ms] D/CompatibilityChangeReporter( 5153): Compat change id reported: 171228096; UID 10269; state: ENABLED
[ +645 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +42 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +6 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +8 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +9 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +9 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +18 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/mpany.sepeshap( 5153): JNI critical lock held for 21.427ms on Thread[51,tid=5427,Runnable,Thread*=0x7678620f60,peer=0x2e2e790,"androidmapsapi-TilePrep_2"]
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +4 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +47 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +8 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +6 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +5 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +6 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +7 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +5 ms] I/PlatformViewsController( 5153): Hosting view in view hierarchy for platform view: 0
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +11 ms] I/PlatformViewsController( 5153): PlatformView is using SurfaceProducer backend
[   +5 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +24 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[   +1 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +96 ms] W/JavaCronetEngine( 5153): using the fallback Cronet Engine implementation. Performance will suffer and many HTTP client features, including caching, will not work.
[   +7 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +21 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[ +144 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +50 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] I/GoogleMapController( 5153): Installing custom TextureView driven invalidator.
[   +5 ms] E/GoogleMapController( 5153): Cannot enable MyLocation layer as location permissions are not granted
[ +123 ms] D/InputConnectionAdaptor( 5153): The input method toggled cursor monitoring off
[  +41 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +22 ms] D/ImageReaderSurfaceProducer( 5153): ImageTextureEntry can't wait on the fence on Android < 33
[  +50 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[ +235 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[ +106 ms] W/DynamiteModule( 5153): Local module descriptor class for com.google.android.gms.googlecertificates not found.
[  +12 ms] I/DynamiteModule( 5153): Considering local module com.google.android.gms.googlecertificates:0 and remote module com.google.android.gms.googlecertificates:7
[   +6 ms] I/DynamiteModule( 5153): Selected remote version of com.google.android.gms.googlecertificates, version >= 7
[   +9 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +5 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +9 ms] W/mpany.sepeshap( 5153): ClassLoaderContext classpath element checksum mismatch. expected=**********, found=22067137
(DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/apex/com.android.extservices/javalib/android.ext.adservices.jar**********]#PCL[/system/framework/com.android
.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/apex/com.android.extservices/javali
b/android.ext.adservices.jar**********]} |
DLC[];PCL[/data/app/~~_iH1Ti3DmlQxnnZZ2d4W9w==/com.sepeshacompany.sepeshapp-nYNv8zcXGbRK0slLnxTisg==/base.apk*22067137]{PCL[/system/framework/org.apache.http.legacy.jar***********]})
[ +102 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[ +142 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +17 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +98 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +21 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +50 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +64 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +65 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +55 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[ +123 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[ +363 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[ +239 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[ +184 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +95 ms] D/CompatibilityChangeReporter( 5153): Compat change id reported: 78294732; UID 10269; state: ENABLED
[  +93 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[ +294 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +67 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[ +109 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +89 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +13 ms] I/mpany.sepeshap( 5153): Background young concurrent copying GC freed 8324KB AllocSpace bytes, 112(5492KB) LOS objects, 46% free, 14MB/27MB, paused 758us,106us total 114.349ms
[ +108 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +88 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +8 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[ +111 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +60 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +78 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +56 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +83 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +29 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +34 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +71 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +88 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[ +768 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +83 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +21 ms] I/mpany.sepeshap( 5153): Compiler allocated 6714KB to compile void m140.ehv.W(m140.emf, m140.fej, android.content.res.Resources, m140.eka, m140.eut, boolean, m140.djk, java.util.Map, boolean,
boolean, boolean, boolean)
[  +31 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +13 ms] I/mpany.sepeshap( 5153): Background concurrent copying GC freed 5786KB AllocSpace bytes, 74(5464KB) LOS objects, 66% free, 21MB/63MB, paused 539us,469us total 279.809ms
[ +185 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +20 ms] I/mpany.sepeshap( 5153): Compiler allocated 7652KB to compile void m140.ehv.W(m140.emf, m140.fej, android.content.res.Resources, m140.eka, m140.eut, boolean, m140.djk, java.util.Map, boolean,
boolean, boolean, boolean)
[  +31 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +31 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +62 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +12 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +36 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +20 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[ +114 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +37 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +33 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +49 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +17 ms] I/mpany.sepeshap( 5153): Compiler allocated 5746KB to compile void m140.epo.o()
[   +1 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +47 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +35 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[   +9 ms] I/mpany.sepeshap( 5153): Compiler allocated 4475KB to compile void m140.ecu.c(m140.djz, m140.ebm, m140.edp, m140.ekm, boolean)
[   +6 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +21 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +24 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +32 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +5 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +22 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +12 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +18 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +44 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[  +73 ms] I/mpany.sepeshap( 5153): Compiler allocated 4190KB to compile m140.emb m140.emf.l(m140.azx, m140.iai, m140.elt, byte[], boolean, m140.cdn, m140.dex, java.lang.Iterable)
[  +72 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +64 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +20 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +65 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +12 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +55 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +24 ms] W/ProxyAndroidLoggerBackend( 5153): Too many Flogger logs received before configuration. Dropping old logs.
[   +8 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +32 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +20 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[+5175 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +24 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +12 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +12 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +12 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +20 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +21 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents( 5153): updateAcquireFence: Did not find frame.