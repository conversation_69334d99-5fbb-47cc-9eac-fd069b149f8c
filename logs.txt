[        ] D/LocationPlugin( 1416): Service connected: ComponentInfo{com.sepeshacompany.sepeshapp/com.lyokone.location.FlutterLocationService}
[ +758 ms] I/flutter ( 1416): Session restored successfully
[   +2 ms] I/flutter ( 1416): Restored user type: null
[+3914 ms] D/ProfileInstaller( 1416): Installing profile for com.sepeshacompany.sepeshapp
[+3975 ms] D/InputMethodManager( 1416): showSoftInput() view=io.flutter.embedding.android.FlutterView{290f5f7 VFE...... .F....ID 0,0-1080,2088 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
[ +302 ms] I/AssistStructure( 1416): Flattened final assist data: 436 bytes, containing 1 windows, 3 views
[ +840 ms] D/InputConnectionAdaptor( 1416): The input method toggled cursor monitoring on
[ +163 ms] D/InsetsController( 1416): show(ime(), fromIme=true)
[+5039 ms] D/InputConnectionAdaptor( 1416): The input method toggled cursor monitoring off
[+2627 ms] I/flutter ( 1416): *********
[  +11 ms] I/flutter ( 1416): === LOGIN REQUEST ===
[   +1 ms] I/flutter ( 1416): URL: https://api.sepesha.com/api/login
[   +5 ms] I/flutter ( 1416): Headers: {Content-Type: application/json}
[   +1 ms] I/flutter ( 1416): Body: {"phone":*********,"user_type":"driver"}
[        ] I/flutter ( 1416): ==================
[+2734 ms] I/flutter ( 1416): === LOGIN RESPONSE ===
[   +1 ms] I/flutter ( 1416): Status Code: 200
[        ] I/flutter ( 1416): Response Headers: {x-powered-by: PHP/8.3.23, alt-svc: h3=":443"; ma=2592000, cache-control: no-cache, private, access-control-allow-origin: *, date: Tue, 22 Jul 2025 07:24:13  
GMT, content-length: 175, content-type: application/json, via: 0.0 Caddy}
[        ] I/flutter ( 1416): Response Body: {"status":true,"message":"OPT Created
successfully","data":{"phone_number":"*********","user_type":"driver","otp":5706,"is_verified":0,"otp_expires_at":"2025-07-22 07:27:13"}}
[        ] I/flutter ( 1416): ==================
[+4523 ms] D/InputMethodManager( 1416): showSoftInput() view=io.flutter.embedding.android.FlutterView{290f5f7 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
[  +28 ms] I/AssistStructure( 1416): Flattened final assist data: 400 bytes, containing 1 windows, 3 views
[  +19 ms] D/InputConnectionAdaptor( 1416): The input method toggled cursor monitoring on
[  +85 ms] D/InsetsController( 1416): show(ime(), fromIme=true)
[+1129 ms] D/InputMethodManager( 1416): showSoftInput() view=io.flutter.embedding.android.FlutterView{290f5f7 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
[   +8 ms] D/InputMethodManager( 1416): showSoftInput() view=io.flutter.embedding.android.FlutterView{290f5f7 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
[  +28 ms] D/InputConnectionAdaptor( 1416): The input method toggled cursor monitoring on
[   +3 ms] I/AssistStructure( 1416): Flattened final assist data: 400 bytes, containing 1 windows, 3 views
[  +11 ms] D/InputConnectionAdaptor( 1416): The input method toggled cursor monitoring off
[   +5 ms] D/InputConnectionAdaptor( 1416): The input method toggled cursor monitoring on
[  +22 ms] D/InsetsController( 1416): show(ime(), fromIme=true)
[        ] D/InsetsController( 1416): show(ime(), fromIme=true)
[        ] D/InsetsController( 1416): show(ime(), fromIme=true)
[ +130 ms] D/InputMethodManager( 1416): showSoftInput() view=io.flutter.embedding.android.FlutterView{290f5f7 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
[  +97 ms] D/InputMethodManager( 1416): showSoftInput() view=io.flutter.embedding.android.FlutterView{290f5f7 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
[   +1 ms] D/InputConnectionAdaptor( 1416): The input method toggled cursor monitoring on
[  +81 ms] I/AssistStructure( 1416): Flattened final assist data: 400 bytes, containing 1 windows, 3 views
[  +26 ms] D/InputConnectionAdaptor( 1416): The input method toggled cursor monitoring off
[   +2 ms] D/InputConnectionAdaptor( 1416): The input method toggled cursor monitoring on
[   +7 ms] D/InsetsController( 1416): show(ime(), fromIme=true)
[  +11 ms] D/InsetsController( 1416): show(ime(), fromIme=true)
[ +492 ms] D/InputMethodManager( 1416): showSoftInput() view=io.flutter.embedding.android.FlutterView{290f5f7 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
[  +95 ms] D/InputMethodManager( 1416): showSoftInput() view=io.flutter.embedding.android.FlutterView{290f5f7 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
[        ] D/InputConnectionAdaptor( 1416): The input method toggled cursor monitoring on
[  +24 ms] I/AssistStructure( 1416): Flattened final assist data: 400 bytes, containing 1 windows, 3 views
[   +7 ms] D/InputConnectionAdaptor( 1416): The input method toggled cursor monitoring off
[  +11 ms] D/InputConnectionAdaptor( 1416): The input method toggled cursor monitoring on
[  +23 ms] D/InsetsController( 1416): show(ime(), fromIme=true)
[        ] D/InsetsController( 1416): show(ime(), fromIme=true)
[        ] D/InsetsController( 1416): show(ime(), fromIme=true)
[ +603 ms] I/flutter ( 1416): === VERIFY OTP REQUEST ===
[   +1 ms] I/flutter ( 1416): URL: https://api.sepesha.com/api/verify-otp
[        ] I/flutter ( 1416): Headers: {Content-Type: application/json}
[        ] I/flutter ( 1416): Body: {"phone":*********,"otp":5706,"user_type":"driver"}
[        ] I/flutter ( 1416): ==================
[+1179 ms] I/flutter ( 1416): === VERIFY OTP RESPONSE ===
[        ] I/flutter ( 1416): Status Code: 200
[        ] I/flutter ( 1416): Response Headers: {x-powered-by: PHP/8.3.23, alt-svc: h3=":443"; ma=2592000, cache-control: no-cache, private, access-control-allow-origin: *, date: Tue, 22 Jul 2025 07:24:22  
GMT, content-encoding: gzip, vary: Accept-Encoding, content-length: 725, via: 0.0 Caddy, content-type: application/json}
[   +1 ms] I/flutter ( 1416): Response Body: {"status":true,"message":"OTP verified
successfully","access_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2tpdW5nby5zZXBlc2hhLmNvbSIsInN1YiI6MiwiaWF0IjoxNzUzMTY5MDYyLCJleHAiOjE3NTMzNDE4NjIsInVzZXJfdHlwZSI6ImRyaXZlciIsImF1dGh
fa2V5IjoiNDA1MzRkZDAtZmI5OS00MjYxLTkzYmUtMDczNWEyNzJmNzlhIiwiZGF0YSI6eyJmaXJzdF9uYW1lIjoidlZ2IiwibWlkZGxlX25hbWUiOiJNaWNoYWVsIiwibGFzdF9uYW1lIjoiRG9lIiwicGhvbmVfbnVtYmVyIjoiNzE0NjA5MTM1IiwicGhvbmVjb2RlIjoiM
jU1IiwiZW1haWwiOiJqb2hudi5kb2VAZXhhbXBsZS5jb20iLCJwcml2YWN5X2NoZWNrZWQiOjEsInVpZCI6IjQwNTM0ZGQwLWZiOTktNDI2MS05M2JlLTA3MzVhMjcyZjc5YSJ9fQ.7tbXku-1Oq5ztA1t1Kh9jUDP46CfuWqEHuaucx9V70Q","refresh_token":"eyJ0eX
AiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTMxNjkwNjIsImV4cCI6MTc1NTc2MTA2Mn0.eWza9MUkECuw0sjm7E7YKinJljMC2T9mx_lpLUkmPNY","user_data":{"first_name":"vVv","middle_name":"Michael","last_name":"Doe","phoneco
de":"255","phone_number":"*********","email":"<EMAIL>","user_type":"driver","uid":"40534dd0-fb99-4261-93be-0735a272f79a"}}
[        ] I/flutter ( 1416): ===================
[  +19 ms] I/flutter ( 1416): User data saved to preferences
[ +673 ms] D/MapsInitializer( 1416): preferredRenderer: null
[   +1 ms] D/zzcc    ( 1416): preferredRenderer: null
[   +2 ms] I/zzcc    ( 1416): Making Creator dynamically
[  +25 ms] W/mpany.sepeshap( 1416): ClassLoaderContext classpath size mismatch. expected=1, found=0
(DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/apex/com.android.extservices/javalib/android.ext.adservices.jar**********]#PCL[/system/framework/com.android
.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/apex/com.android.extservices/javali
b/android.ext.adservices.jar**********]} | DLC[];PCL[])
[   +5 ms] I/DynamiteModule( 1416): Considering local module com.google.android.gms.maps_core_dynamite:0 and remote module com.google.android.gms.maps_core_dynamite:252130101
[        ] I/DynamiteModule( 1416): Selected remote version of com.google.android.gms.maps_core_dynamite, version >= 252130101
[        ] V/DynamiteModule( 1416): Dynamite loader version >= 2, using loadModule2NoCrashUtils
[  +19 ms] W/System  ( 1416): ClassLoader referenced unknown path: 
[   +1 ms] D/nativeloader( 1416): Configuring clns-6 for other apk . target_sdk_version=36, uses_libraries=,
library_path=/data/app/~~4dvlENgeESzcZBvZMa7cfA==/com.google.android.gms-HZfmX2H3MQu93nfHphp9UA==/lib/arm64:/data/app/~~4dvlENgeESzcZBvZMa7cfA==/com.google.android.gms-HZfmX2H3MQu93nfHphp9UA==/base.apk!/lib
/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
[ +586 ms] I/mpany.sepeshap( 1416): hiddenapi: Accessing hidden method Ldalvik/system/VMStack;->getStackClass2()Ljava/lang/Class; (runtime_flags=0, domain=core-platform, api=unsupported) from Lm140/gmg;
(domain=app) using reflection: allowed
[  +52 ms] D/nativeloader( 1416): Configuring clns-7 for other apk . target_sdk_version=35, uses_libraries=ALL,
library_path=/data/user_de/0/com.google.android.gms/app_chimera/m/0000000b/dl-MapsCoreDynamite.integ_252130101100400.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
[  +14 ms] D/nativeloader( 1416): Load /data/user_de/0/com.google.android.gms/app_chimera/m/0000000b/dl-MapsCoreDynamite.integ_252130101100400.apk!/lib/arm64-v8a/libgmm-jni.so using isolated ns clns-7
(caller=/data/user_de/0/com.google.android.gms/app_chimera/m/0000000b/dl-MapsCoreDynamite.integ_252130101100400.apk): ok
[   +1 ms] I/native  ( 1416): I0000 00:00:1753169062.884435    1416 jni_init.cc:30] Initializing JNI...
[   +5 ms] I/Google Android Maps SDK( 1416): Google Play services client version: 18020000
[  +40 ms] D/bo      ( 1416): SDK type: 1, version: 252130101
[  +15 ms] D/ho      ( 1416): maps_core_dynamite module version in use (0 represents standalone library): 252130101
[   +1 ms] D/ho      ( 1416): Added event: 109
[   +1 ms] D/ho      ( 1416): Added event: 112
[        ] D/MapsInitializer( 1416): loadedRenderer: LATEST
[        ] D/zzcc    ( 1416): preferredRenderer: null
[   +2 ms] D/bo      ( 1416): SDK type: 1, version: 252130101
[        ] I/Google Android Maps SDK( 1416): Google Play services package version: 252635029
[        ] I/Google Android Maps SDK( 1416): Google Play services maps renderer version(maps_core): 252130101
[        ] D/bo      ( 1416): SDK type: 1, version: 252130101
[   +1 ms] D/de      ( 1416): about to start loading native library asynchronously
[   +9 ms] W/x       ( 1416): Suppressed StrictMode policy violation: StrictModeDiskReadViolation
[   +4 ms] I/o       ( 1416): Using GMM server: https://clients4.google.com/glm/mmap
[        ] W/x       ( 1416): Suppressed StrictMode policy violation: StrictModeDiskReadViolation
[        ] W/x       ( 1416): Suppressed StrictMode policy violation: StrictModeDiskWriteViolation
[   +2 ms] W/x       ( 1416): Suppressed StrictMode policy violation: StrictModeDiskReadViolation
[   +1 ms] D/o       ( 1416): Using Non-null serverVersionMetadataManager to load previous metadata.
[  +29 ms] I/bs      ( 1416): Selected MapView map renderer: P
[   +1 ms] D/ho      ( 1416): maps_core_dynamite module version in use (0 represents standalone library): 252130101
[  +38 ms] D/CompatibilityChangeReporter( 1416): Compat change id reported: 171228096; UID 10269; state: ENABLED
[ +186 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +24 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +74 ms] W/Settings( 1416): Setting device_provisioned has moved from android.provider.Settings.System to android.provider.Settings.Global, returning read-only value.
[        ] W/Settings( 1416): Setting device_provisioned has moved from android.provider.Settings.System to android.provider.Settings.Global, returning read-only value.
[   +6 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +6 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +16 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +9 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +18 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/JavaCronetEngine( 1416): using the fallback Cronet Engine implementation. Performance will suffer and many HTTP client features, including caching, will not work.
[   +3 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +8 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[ +184 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +6 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +4 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +43 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +4 ms] I/PlatformViewsController( 1416): Hosting view in view hierarchy for platform view: 0
[   +9 ms] I/PlatformViewsController( 1416): PlatformView is using SurfaceProducer backend
[  +14 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[ +361 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +46 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] I/GoogleMapController( 1416): Installing custom TextureView driven invalidator.
[   +2 ms] E/GoogleMapController( 1416): Cannot enable MyLocation layer as location permissions are not granted
[  +73 ms] D/InputConnectionAdaptor( 1416): The input method toggled cursor monitoring off
[  +26 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +8 ms] D/ImageReaderSurfaceProducer( 1416): ImageTextureEntry can't wait on the fence on Android < 33
[  +15 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +14 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +74 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +51 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +18 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +56 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +18 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +9 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +20 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +11 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +25 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +21 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +6 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +27 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +7 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +8 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +13 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +8 ms] W/ImageReader_JNI( 1416): Unable to acquire a buffer item, very likely client tried to acquire more than maxImages buffers
[   +2 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +10 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +2 ms] W/ImageReader_JNI( 1416): Unable to acquire a buffer item, very likely client tried to acquire more than maxImages buffers
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +7 ms] W/DynamiteModule( 1416): Local module descriptor class for com.google.android.gms.googlecertificates not found.
[   +7 ms] I/DynamiteModule( 1416): Considering local module com.google.android.gms.googlecertificates:0 and remote module com.google.android.gms.googlecertificates:7
[   +1 ms] I/DynamiteModule( 1416): Selected remote version of com.google.android.gms.googlecertificates, version >= 7
[   +1 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +3 ms] W/mpany.sepeshap( 1416): ClassLoaderContext classpath element checksum mismatch. expected=**********, found=22067137
(DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/apex/com.android.extservices/javalib/android.ext.adservices.jar**********]#PCL[/system/framework/com.android
.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/apex/com.android.extservices/javali
b/android.ext.adservices.jar**********]} |
DLC[];PCL[/data/app/~~6fPs6_Zx08FT8TVw8I2gwQ==/com.sepeshacompany.sepeshapp-SyIw98Y87iRMCa3e7LhyRA==/base.apk*22067137]{PCL[/system/framework/org.apache.http.legacy.jar***********]})
[  +11 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +3 ms] W/ImageReader_JNI( 1416): Unable to acquire a buffer item, very likely client tried to acquire more than maxImages buffers
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +2 ms] W/ImageReader_JNI( 1416): Unable to acquire a buffer item, very likely client tried to acquire more than maxImages buffers
[  +13 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +11 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +5 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +11 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +4 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +4 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +6 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +1 ms] W/ImageReader_JNI( 1416): Unable to acquire a buffer item, very likely client tried to acquire more than maxImages buffers
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +23 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +11 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +4 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +4 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +9 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +16 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +4 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +8 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +8 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +14 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +7 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +8 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[ +955 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[ +166 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +4 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[ +186 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +96 ms] I/mpany.sepeshap( 1416): Background young concurrent copying GC freed 7327KB AllocSpace bytes, 87(9316KB) LOS objects, 55% free, 12MB/27MB, paused 853us,178us total 116.875ms
[ +105 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +6 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +34 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +4 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +51 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +69 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[ +110 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +67 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +36 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +69 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +81 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[ +163 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[ +188 ms] I/mpany.sepeshap( 1416): Compiler allocated 6723KB to compile void m140.ehv.W(m140.emf, m140.fej, android.content.res.Resources, m140.eka, m140.eut, boolean, m140.djk, java.util.Map, boolean,
boolean, boolean, boolean)
[ +300 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +10 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +10 ms] I/mpany.sepeshap( 1416): Background concurrent copying GC freed 5849KB AllocSpace bytes, 65(7448KB) LOS objects, 66% free, 17MB/53MB, paused 183us,184us total 126.595ms
[   +4 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +86 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +12 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +20 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[        ] W/ImageReader_JNI( 1416): Unable to acquire a buffer item, very likely client tried to acquire more than maxImages buffers
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +36 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +12 ms] I/mpany.sepeshap( 1416): Compiler allocated 5744KB to compile void m140.epo.o()
[   +5 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +66 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +33 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +5 ms] I/mpany.sepeshap( 1416): Compiler allocated 4475KB to compile void m140.ecu.c(m140.djz, m140.ebm, m140.edp, m140.ekm, boolean)
[  +11 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +50 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +89 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +59 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +6 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +9 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[   +3 ms] W/ImageReader_JNI( 1416): Unable to acquire a buffer item, very likely client tried to acquire more than maxImages buffers
[  +13 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +26 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +69 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +54 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +34 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +18 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +5 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +10 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +12 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +30 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[  +21 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +10 ms] W/ProxyAndroidLoggerBackend( 1416): Too many Flogger logs received before configuration. Dropping old logs.
[   +4 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +68 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +20 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents( 1416): updateAcquireFence: Did not find frame.